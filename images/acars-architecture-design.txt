# ACARS架构图设计说明

## 架构图层次结构

1. **数据接入层（顶部）**
   - 左侧：自建设备（格式A）
   - 中间左：成都天府机场（格式A）
   - 中间右：长沙机场（格式B）
   - 右侧：Airframes（格式C）
   - 底部连接：多源适配器（每个数据源对应一个适配器）

2. **数据接收服务（第二层）**
   - 中间：统一数据接收服务
   - 连接到下方的Kafka消息队列

3. **消息中间件（第三层）**
   - Kafka集群
   - 显示三个主题：acars-raw-format-a、acars-raw-format-b、acars-raw-format-c
   - 右侧：标准化数据主题acars-standardized

4. **数据处理层（第四层）**
   - 左侧：格式解析器（A、B、C三种格式）
   - 中间：数据标准化服务
   - 右侧：数据富集服务

5. **数据存储层（第五层）**
   - 左侧：实时数据库
   - 中间：历史数据库
   - 右侧：数据仓库

6. **应用服务层（第六层）**
   - 顶部：API网关
   - 下方：各种业务服务（航班监控、数据分析、报警、报表等）

7. **监控与管理（右侧垂直）**
   - 系统监控
   - 告警系统
   - 管理控制台

## 数据流向

使用箭头表示数据流向：

1. 数据源 → 多源适配器 → 数据接收服务
2. 数据接收服务 → Kafka原始数据主题
3. Kafka原始数据主题 → 格式解析器 → 数据标准化服务
4. 数据标准化服务 → Kafka标准化数据主题
5. Kafka标准化数据主题 → 数据富集服务
6. 数据富集服务 → 数据存储（实时数据库、历史数据库、数据仓库）
7. 数据存储 → API网关 → 业务服务

## 图例设计

- 使用不同颜色区分不同的层次
- 使用实线箭头表示数据主流向
- 使用虚线箭头表示控制流或次要数据流
- 为每个组件添加简要说明