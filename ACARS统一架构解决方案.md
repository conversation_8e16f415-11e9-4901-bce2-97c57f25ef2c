# ACARS统一数据处理架构解决方案

## 项目背景

现有的ACARS报文数据有多种不同来源和格式，需要进行统一处理。当前存在以下数据源：

1. **自建设备上传程序**（数据格式A）
   - 服务：acars-msg-receiver (120.133.0.153)
   - 推送到topic: acars-packet
   - 包括4G方式上传

2. **成都天府机场kafka consumer**（数据格式A）
   - 同样使用acars-msg-receiver (120.133.0.153)

3. **长沙机场推送**（数据格式B）
   - 服务：airport-packet-transfer (120.133.0.236)
   - 专线接收kafka，转推到topic: acars-packet-csx

4. **直接抓取airframes**（数据格式C）
   - 服务：acars-msg-parser (120.133.0.162)

## 核心问题分析

- **多种数据格式**：至少有A、B、C三种不同格式
- **多个数据源**：不同的接收方式和推送机制
- **分散的处理服务**：多个不同的服务器和组件
- **不同的topic**：数据分散在不同的kafka topic中

## 解决方案设计

### 核心设计理念

基于**适配器模式**和**事件驱动架构**，构建一个可扩展的统一数据处理平台，实现：
- 数据源解耦
- 格式标准化  
- 统一处理流程
- 灵活扩展能力

### 整体架构图

```mermaid
graph TB
    subgraph "数据源层 Data Sources"
        DS1[自建设备上传<br/>格式A]
        DS2[成都天府机场<br/>格式A]
        DS3[长沙机场推送<br/>格式B]
        DS4[Airframes抓取<br/>格式C]
        DS5[未来数据源<br/>格式X]
    end

    subgraph "数据接入层 Data Ingestion"
        A1[设备上传适配器]
        A2[机场Kafka适配器]
        A3[专线接收适配器]
        A4[Web抓取适配器]
        A5[通用适配器]
    end

    subgraph "消息总线 Message Bus"
        MB[统一消息总线<br/>Kafka/RabbitMQ]
    end

    subgraph "数据处理层 Processing Layer"
        NP[格式标准化处理器]
        VP[数据验证处理器]
        EP[数据增强处理器]
        RP[路由分发处理器]
    end

    subgraph "数据存储层 Storage Layer"
        RS[原始数据存储]
        PS[处理后数据存储]
        MS[元数据存储]
    end

    subgraph "应用服务层 Application Services"
        AS1[实时监控服务]
        AS2[数据分析服务]
        AS3[报警服务]
        AS4[API网关]
    end

    DS1 --> A1
    DS2 --> A2
    DS3 --> A3
    DS4 --> A4
    DS5 --> A5

    A1 --> MB
    A2 --> MB
    A3 --> MB
    A4 --> MB
    A5 --> MB

    MB --> NP
    NP --> VP
    VP --> EP
    EP --> RP

    RP --> RS
    RP --> PS
    RP --> MS

    PS --> AS1
    PS --> AS2
    PS --> AS3
    PS --> AS4
```

### 架构层次

#### 1. 数据源层 (Data Sources)
- 自建设备上传（格式A）
- 成都天府机场（格式A）
- 长沙机场推送（格式B）
- Airframes抓取（格式C）
- 未来数据源（格式X）

#### 2. 数据接入层 (Data Ingestion)
每种数据源对应一个专用适配器：
- 设备上传适配器
- 机场Kafka适配器
- 专线接收适配器
- Web抓取适配器
- 通用适配器

**适配器职责**：
- 数据格式转换
- 连接管理
- 错误处理
- 数据质量初检

**适配器接口标准**：
```java
@Component
public interface DataSourceAdapter {
    /**
     * 建立连接
     */
    CompletableFuture<Boolean> connect();

    /**
     * 获取数据流
     */
    Flux<RawMessage> fetchData();

    /**
     * 转换为标准格式
     */
    StandardMessage transformToStandard(RawMessage rawData) throws TransformException;

    /**
     * 错误处理
     */
    void handleError(Exception error);

    /**
     * 获取健康状态
     */
    HealthStatus getHealthStatus();

    /**
     * 关闭连接
     */
    void close();
}
```

#### 3. 消息总线 (Message Bus)
统一消息总线（推荐Kafka/RabbitMQ）

#### 4. 数据处理层 (Processing Layer)
- **格式标准化处理器**：将各种格式转换为统一格式
- **数据验证处理器**：检查数据完整性和有效性
- **数据增强处理器**：添加地理信息、航班信息等
- **路由分发处理器**：根据消息类型和业务需求分发

#### 5. 数据存储层 (Storage Layer)
- **原始数据存储**：保存未处理的原始数据
- **处理后数据存储**：保存标准化后的数据
- **元数据存储**：保存配置和状态信息

#### 6. 应用服务层 (Application Services)
- **实时监控服务**
- **数据分析服务**
- **报警服务**
- **API网关**

## 统一消息格式

**标准ACARS消息结构**：
```json
{
  "message_id": "uuid",
  "timestamp": "2024-01-01T12:00:00Z",
  "source": "source_identifier",
  "source_format": "A|B|C",
  "aircraft": {
    "tail_number": "B-1234",
    "flight_number": "CA1234"
  },
  "message": {
    "type": "position|weather|maintenance",
    "content": "原始消息内容",
    "parsed_data": {}
  },
  "metadata": {
    "quality_score": 0.95,
    "processing_flags": []
  }
}
```

## 技术选型建议

### 消息总线
**Apache Kafka**
- 高吞吐量
- 持久化存储
- 分区支持
- 生态完善

### 数据存储
- **原始数据**：Apache Kafka（短期）+ HDFS/S3（长期）
- **处理后数据**：ClickHouse（分析）+ Redis（缓存）
- **元数据**：PostgreSQL

### 开发语言
**统一Java技术栈**：
- **适配器层**：Java + Spring Boot（企业级框架，易维护）
- **处理层**：Java + Spring Cloud Stream（流处理）
- **服务层**：Java + Spring Boot + Spring Cloud（微服务架构）
- **数据访问层**：Spring Data JPA + MyBatis（数据持久化）

## 实施优势

1. **可扩展性**：新增数据源只需开发对应适配器
2. **容错性**：单个适配器故障不影响整体系统
3. **标准化**：统一的数据格式便于后续处理
4. **监控性**：每个环节都可独立监控
5. **维护性**：模块化设计便于维护和升级

## 实施建议

### 第一阶段：基础架构搭建
1. 搭建Kafka消息总线
2. 开发适配器框架
3. 实现基础的格式标准化处理器

### 第二阶段：适配器开发
1. 开发现有数据源的适配器
2. 实现数据验证和增强功能
3. 建立监控和告警机制

### 第三阶段：优化和扩展
1. 性能优化
2. 添加新的数据源支持
3. 完善分析和应用服务

## 迁移策略

1. **并行运行**：新系统与现有系统并行运行
2. **逐步迁移**：按数据源逐个迁移
3. **数据对比**：确保数据一致性
4. **平滑切换**：最小化业务影响

---

*文档创建时间：2025-08-01*
*版本：v1.0*
