# ACARS项目详细改造方案

## 现有项目分析

### 1. acars-msg-receiver (120.133.0.153)
**功能**：处理自建设备上传和成都天府机场的数据（格式A）
**当前状态**：
- 推送到生产环境topic: acars-packet
- 处理4G方式上传的数据
- 同时服务两个数据源

**改造难点**：
- 单一服务处理多个数据源，耦合度高
- 格式A的具体结构需要分析
- 可能存在性能瓶颈

### 2. airport-packet-transfer (120.133.0.236)
**功能**：处理长沙机场推送数据（格式B）
**当前状态**：
- 专线接收kafka数据
- 转推到生产环境topic: acars-packet-csx
- 独立的topic处理

**改造难点**：
- 专线连接的稳定性保障
- 格式B与其他格式的差异
- 网络配置和安全考虑

### 3. acars-msg-parser (120.133.0.162)
**功能**：直接抓取airframes数据（格式C）
**当前状态**：
- Web抓取方式获取数据
- 格式C处理

**改造难点**：
- Web抓取的反爬虫机制
- 数据实时性要求
- 外部依赖的稳定性

## 详细改造方案

### 阶段一：基础设施准备（4-6周）

#### 1.1 统一消息总线搭建（2周）
**目标**：建立Kafka集群作为统一消息总线

**具体任务**：
- 部署Kafka集群（3节点，高可用）
- 配置topic策略：
  - `acars-raw-data`：接收所有原始数据
  - `acars-standard-data`：标准化后的数据
  - `acars-processed-data`：最终处理结果
- 设置监控和告警
- 性能调优和容量规划

**技术要求**：
```yaml
Kafka配置:
  版本: 3.5+
  节点数: 3
  副本因子: 3
  分区数: 12 (可根据吞吐量调整)
  保留时间: 7天 (原始数据)
```

#### 1.2 适配器框架开发（2周）
**目标**：开发通用适配器框架

**核心组件**：
```go
// 适配器接口定义
type DataSourceAdapter interface {
    Connect() error
    FetchData() <-chan RawMessage
    TransformToStandard(raw RawMessage) (*StandardMessage, error)
    HandleError(err error)
    GetHealthStatus() HealthStatus
    Close() error
}

// 标准消息结构
type StandardMessage struct {
    MessageID    string                 `json:"message_id"`
    Timestamp    time.Time             `json:"timestamp"`
    Source       string                `json:"source"`
    SourceFormat string                `json:"source_format"`
    Aircraft     AircraftInfo          `json:"aircraft"`
    Message      MessageContent        `json:"message"`
    Metadata     MessageMetadata       `json:"metadata"`
}
```

#### 1.3 数据处理流水线（2周）
**目标**：实现标准化处理流水线

**处理器链**：
1. **格式标准化处理器**
2. **数据验证处理器**
3. **数据增强处理器**
4. **路由分发处理器**

### 阶段二：适配器开发（6-8周）

#### 2.1 设备上传适配器开发（2周）
**目标**：替换acars-msg-receiver的设备上传功能

**实现要点**：
- 保持现有接口兼容性
- 支持4G上传协议
- 实现格式A到标准格式的转换
- 添加数据质量检查

**部署策略**：
- 并行部署，逐步切换流量
- 保留原有服务作为备份

#### 2.2 机场Kafka适配器开发（2周）
**目标**：处理成都天府机场数据

**实现要点**：
- Kafka consumer实现
- 格式A数据解析
- 错误重试机制
- 偏移量管理

#### 2.3 专线接收适配器开发（2周）
**目标**：替换airport-packet-transfer功能

**实现要点**：
- 专线连接管理
- 格式B到标准格式转换
- 网络异常处理
- 数据完整性校验

**特殊考虑**：
- 专线网络的特殊性
- 安全认证机制
- 故障切换策略

#### 2.4 Web抓取适配器开发（2周）
**目标**：替换acars-msg-parser功能

**实现要点**：
- 反爬虫策略
- 格式C数据解析
- 增量抓取机制
- 代理池管理

### 阶段三：迁移实施（4-6周）

#### 3.1 数据对比验证（2周）
**目标**：确保新旧系统数据一致性

**验证方案**：
- 双写模式：同时写入新旧系统
- 数据对比工具开发
- 差异分析和修正
- 性能基准测试

#### 3.2 灰度切换（2周）
**目标**：逐步切换到新系统

**切换策略**：
```
第1天: 10%流量 → 新系统
第3天: 30%流量 → 新系统
第5天: 50%流量 → 新系统
第7天: 80%流量 → 新系统
第10天: 100%流量 → 新系统
```

#### 3.3 旧系统下线（2周）
**目标**：安全下线旧系统

**下线步骤**：
- 停止新数据写入旧系统
- 数据归档和备份
- 服务器资源回收
- 监控和告警调整

## 风险评估与应对

### 高风险项
1. **专线网络稳定性**
   - 风险：专线中断导致数据丢失
   - 应对：建立备用连接，实现自动切换

2. **数据格式兼容性**
   - 风险：格式转换错误导致数据损坏
   - 应对：充分测试，建立数据校验机制

3. **性能瓶颈**
   - 风险：新系统性能不如旧系统
   - 应对：压力测试，性能优化

### 中风险项
1. **Web抓取反爬虫**
   - 风险：被目标网站封禁
   - 应对：多IP轮换，请求频率控制

2. **Kafka集群故障**
   - 风险：消息总线不可用
   - 应对：高可用部署，快速故障恢复

## 回滚计划

### 快速回滚（15分钟内）
- 流量切换回旧系统
- DNS/负载均衡调整
- 监控告警恢复

### 完整回滚（2小时内）
- 数据同步回旧系统
- 配置文件恢复
- 服务重启和验证

## 成功标准

### 功能指标
- [ ] 数据完整性：99.9%
- [ ] 数据准确性：99.95%
- [ ] 系统可用性：99.9%

### 性能指标
- [ ] 数据处理延迟：< 5秒
- [ ] 系统吞吐量：≥ 现有系统
- [ ] 错误率：< 0.1%

### 运维指标
- [ ] 部署时间：< 30分钟
- [ ] 故障恢复时间：< 15分钟
- [ ] 监控覆盖率：100%

## 详细实施时间表

### 总体时间线：14-20周

```mermaid
gantt
    title ACARS改造项目时间表
    dateFormat  YYYY-MM-DD
    section 阶段一：基础设施
    Kafka集群搭建           :done, kafka, 2025-08-01, 2w
    适配器框架开发          :done, framework, 2025-08-15, 2w
    数据处理流水线          :active, pipeline, 2025-08-29, 2w

    section 阶段二：适配器开发
    设备上传适配器          :adapter1, after pipeline, 2w
    机场Kafka适配器         :adapter2, after adapter1, 2w
    专线接收适配器          :adapter3, after adapter2, 2w
    Web抓取适配器          :adapter4, after adapter3, 2w

    section 阶段三：迁移实施
    数据对比验证           :validation, after adapter4, 2w
    灰度切换              :migration, after validation, 2w
    旧系统下线            :cleanup, after migration, 2w
```

### 每周详细计划

#### 第1-2周：Kafka集群搭建
**目标**：建立生产级Kafka集群

**第1周**：
- 周一：服务器资源申请和环境准备
- 周二：Kafka安装和基础配置
- 周三：集群配置和网络调试
- 周四：监控系统集成
- 周五：基础功能测试

**第2周**：
- 周一：性能调优和压力测试
- 周二：高可用性测试
- 周三：安全配置和权限管理
- 周四：运维文档编写
- 周五：验收和交付

#### 第3-4周：适配器框架开发
**目标**：完成通用适配器框架

**技术栈**：
- 语言：Go 1.21+
- 消息队列：Kafka Go客户端
- 配置管理：Viper
- 日志：Logrus
- 监控：Prometheus + Grafana

**核心模块**：
```go
// 配置管理
type AdapterConfig struct {
    Name           string        `yaml:"name"`
    Source         SourceConfig  `yaml:"source"`
    Target         TargetConfig  `yaml:"target"`
    Transform      TransformConfig `yaml:"transform"`
    Retry          RetryConfig   `yaml:"retry"`
    Health         HealthConfig  `yaml:"health"`
}

// 重试机制
type RetryConfig struct {
    MaxRetries    int           `yaml:"max_retries"`
    InitialDelay  time.Duration `yaml:"initial_delay"`
    MaxDelay      time.Duration `yaml:"max_delay"`
    Multiplier    float64       `yaml:"multiplier"`
}

// 健康检查
type HealthConfig struct {
    CheckInterval time.Duration `yaml:"check_interval"`
    Timeout       time.Duration `yaml:"timeout"`
    FailThreshold int           `yaml:"fail_threshold"`
}
```

## 技术实现细节

### 数据格式转换规范

#### 格式A → 标准格式
```go
func TransformFormatA(raw []byte) (*StandardMessage, error) {
    // 格式A的具体解析逻辑
    var formatA FormatAMessage
    if err := json.Unmarshal(raw, &formatA); err != nil {
        return nil, fmt.Errorf("failed to parse format A: %w", err)
    }

    return &StandardMessage{
        MessageID:    generateUUID(),
        Timestamp:    formatA.Timestamp,
        Source:       "format_a_source",
        SourceFormat: "A",
        Aircraft: AircraftInfo{
            TailNumber:   formatA.Aircraft.Registration,
            FlightNumber: formatA.Flight.Number,
        },
        Message: MessageContent{
            Type:       detectMessageType(formatA.Content),
            Content:    formatA.Content,
            ParsedData: parseFormatAContent(formatA.Content),
        },
        Metadata: MessageMetadata{
            QualityScore:     calculateQuality(formatA),
            ProcessingFlags:  []string{},
        },
    }, nil
}
```

#### 格式B → 标准格式
```go
func TransformFormatB(raw []byte) (*StandardMessage, error) {
    // 格式B的具体解析逻辑
    // 长沙机场专用格式处理
    var formatB FormatBMessage
    if err := parseFormatB(raw, &formatB); err != nil {
        return nil, fmt.Errorf("failed to parse format B: %w", err)
    }

    return &StandardMessage{
        MessageID:    generateUUID(),
        Timestamp:    formatB.ReceivedTime,
        Source:       "changsha_airport",
        SourceFormat: "B",
        Aircraft: AircraftInfo{
            TailNumber:   formatB.AircraftID,
            FlightNumber: formatB.FlightID,
        },
        Message: MessageContent{
            Type:       formatB.MessageType,
            Content:    formatB.RawContent,
            ParsedData: formatB.ParsedFields,
        },
        Metadata: MessageMetadata{
            QualityScore:     formatB.QualityIndicator,
            ProcessingFlags:  validateFormatB(formatB),
        },
    }, nil
}
```

### 监控和告警配置

#### Prometheus指标定义
```yaml
# 适配器性能指标
acars_adapter_messages_total{adapter="device_upload", status="success"}
acars_adapter_messages_total{adapter="device_upload", status="error"}
acars_adapter_processing_duration_seconds{adapter="device_upload"}
acars_adapter_connection_status{adapter="device_upload"}

# 数据质量指标
acars_data_quality_score{source="format_a"}
acars_data_validation_errors_total{type="missing_field"}
acars_data_transformation_errors_total{format="A"}

# 系统健康指标
acars_kafka_lag_seconds{topic="acars-raw-data"}
acars_processing_pipeline_latency_seconds{stage="normalization"}
```

#### 告警规则
```yaml
groups:
- name: acars_alerts
  rules:
  - alert: AcarsAdapterDown
    expr: acars_adapter_connection_status == 0
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "ACARS适配器连接中断"

  - alert: AcarsHighErrorRate
    expr: rate(acars_adapter_messages_total{status="error"}[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "ACARS错误率过高"

  - alert: AcarsDataQualityLow
    expr: avg(acars_data_quality_score) < 0.9
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "ACARS数据质量下降"
```

## 部署和运维

### Docker化部署
```dockerfile
# 适配器基础镜像
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod download
RUN go build -o adapter ./cmd/adapter

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/adapter .
COPY --from=builder /app/configs ./configs
CMD ["./adapter"]
```

### Kubernetes部署配置
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: acars-device-adapter
spec:
  replicas: 3
  selector:
    matchLabels:
      app: acars-device-adapter
  template:
    metadata:
      labels:
        app: acars-device-adapter
    spec:
      containers:
      - name: adapter
        image: acars/device-adapter:v1.0
        ports:
        - containerPort: 8080
        env:
        - name: KAFKA_BROKERS
          value: "kafka-cluster:9092"
        - name: ADAPTER_CONFIG
          value: "/etc/config/adapter.yaml"
        volumeMounts:
        - name: config
          mountPath: /etc/config
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: config
        configMap:
          name: adapter-config
```

---

*文档创建时间：2025-08-01*
*版本：v1.0*
