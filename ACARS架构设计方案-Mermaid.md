# ACARS架构设计方案 - Mermaid图

```mermaid
graph TB
    %% 定义样式
    classDef sourceLayer fill:#f9d5e5,stroke:#333,stroke-width:1px;
    classDef adapterLayer fill:#eeeeee,stroke:#333,stroke-width:1px;
    classDef receiverLayer fill:#d5e8d4,stroke:#333,stroke-width:1px;
    classDef messageLayer fill:#dae8fc,stroke:#333,stroke-width:1px;
    classDef processLayer fill:#fff2cc,stroke:#333,stroke-width:1px;
    classDef storageLayer fill:#e1d5e7,stroke:#333,stroke-width:1px;
    classDef appLayer fill:#d5e8d4,stroke:#333,stroke-width:1px;
    classDef monitorLayer fill:#f8cecc,stroke:#333,stroke-width:1px;

    %% 数据源层
    subgraph 数据源层
        S1[自建设备<br>格式A]:::sourceLayer
        S2[成都天府机场<br>格式A]:::sourceLayer
        S3[长沙机场<br>格式B]:::sourceLayer
        S4[Airframes<br>格式C]:::sourceLayer
    end

    %% 适配器层
    subgraph 数据接入层
        A1[自建设备适配器]:::adapterLayer
        A2[机场Kafka适配器]:::adapterLayer
        A3[专线接收适配器]:::adapterLayer
        A4[Web抓取适配器]:::adapterLayer
        A5[通用HTTP/HTTPS适配器]:::adapterLayer
        A6[通用Socket适配器]:::adapterLayer
    end

    %% 数据接收服务
    R1[数据接收服务]:::receiverLayer

    %% 消息中间件
    subgraph 消息中间件
        K1[Kafka集群]:::messageLayer
        KT1[acars-raw-format-a]:::messageLayer
        KT2[acars-raw-format-b]:::messageLayer
        KT3[acars-raw-format-c]:::messageLayer
        KT4[acars-standardized]:::messageLayer
    end

    %% 数据处理层
    subgraph 数据处理层
        P1[格式A解析器]:::processLayer
        P2[格式B解析器]:::processLayer
        P3[格式C解析器]:::processLayer
        P4[数据标准化服务]:::processLayer
        P5[数据富集服务]:::processLayer
    end

    %% 数据存储层
    subgraph 数据存储层
        DB1[实时数据库<br>MongoDB]:::storageLayer
        DB2[历史数据库<br>ClickHouse]:::storageLayer
        DB3[数据仓库<br>Apache Druid]:::storageLayer
    end

    %% 应用服务层
    subgraph 应用服务层
        API[API网关]:::appLayer
        BS1[航班监控服务]:::appLayer
        BS2[数据分析服务]:::appLayer
        BS3[报警服务]:::appLayer
        BS4[报表服务]:::appLayer
    end

    %% 监控与管理
    subgraph 监控与管理
        M1[系统监控<br>Prometheus+Grafana]:::monitorLayer
        M2[告警系统]:::monitorLayer
        M3[管理控制台]:::monitorLayer
    end

    %% 连接关系
    S1 --> A1
    S2 --> A2
    S3 --> A3
    S4 --> A4

    A1 --> R1
    A2 --> R1
    A3 --> R1
    A4 --> R1
    A5 --> R1
    A6 --> R1

    R1 --> K1
    K1 --> KT1
    K1 --> KT2
    K1 --> KT3

    KT1 --> P1
    KT2 --> P2
    KT3 --> P3

    P1 --> P4
    P2 --> P4
    P3 --> P4

    P4 --> KT4
    KT4 --> P5

    P5 --> DB1
    P5 --> DB2
    P5 --> DB3

    DB1 --> API
    DB2 --> API
    DB3 --> API

    API --> BS1
    API --> BS2
    API --> BS3
    API --> BS4

    %% 监控连接
    M1 -.-> R1
    M1 -.-> K1
    M1 -.-> P4
    M1 -.-> DB1
    M2 -.-> M1
    M3 -.-> M1
    M3 -.-> M2
```

## 架构说明

该Mermaid图展示了ACARS数据处理的完整架构，包含以下几个核心层次：

### 1. 数据源层
- 自建设备（格式A）
- 成都天府机场（格式A）
- 长沙机场（格式B）
- Airframes（格式C）

### 2. 数据接入层
- 各类适配器，负责从不同来源接收数据
- 通用适配器，用于未来扩展

### 3. 数据接收服务
- 统一的数据接收入口
- 实现基本的数据验证和过滤

### 4. 消息中间件
- Kafka集群
- 不同格式的原始数据主题
- 标准化数据主题

### 5. 数据处理层
- 格式解析器（针对不同格式）
- 数据标准化服务
- 数据富集服务

### 6. 数据存储层
- 实时数据库（MongoDB）
- 历史数据库（ClickHouse）
- 数据仓库（Apache Druid）

### 7. 应用服务层
- API网关
- 各种业务服务

### 8. 监控与管理
- 系统监控
- 告警系统
- 管理控制台

## 数据流向

1. 各数据源通过对应的适配器将数据发送到数据接收服务
2. 数据接收服务将原始数据按格式分类发送到Kafka对应主题
3. 格式解析器从Kafka消费原始数据并解析
4. 标准化服务将解析后的数据转换为统一格式
5. 富集服务从标准化主题消费数据并补充额外信息
6. 富集后的数据存储到不同的数据库
7. 业务服务通过API网关访问数据
8. 监控系统实时监控整个数据流和系统状态