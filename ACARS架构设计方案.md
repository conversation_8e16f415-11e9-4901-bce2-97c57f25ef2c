# ACARS架构设计方案

## 现状分析

根据现有文档，ACARS报文数据来自多个不同的数据源，具有不同的数据格式：

1. **自建设备上传程序**：数据格式A，通过acars-msg-receiver(120.133.0.153)推送到生产环境topic: acars-packet
2. **成都天府机场kafka consumer**：数据格式A，同样通过acars-msg-receiver(120.133.0.153)处理
3. **长沙机场推送**：数据格式B，通过airport-packet-transfer(120.133.0.236)专线接收kafka，转推到生产环境topic: acars-packet-csx
4. **直接抓取airframes**：数据格式C，通过acars-msg-parser(120.133.0.162)处理

当前架构面临的主要挑战：
- 多种数据源和数据格式需要不同的处理逻辑
- 系统扩展性受限，新增数据源或格式需要开发新的处理模块
- 缺乏统一的数据处理流程和标准

## 新架构设计

### 1. 整体架构

![ACARS架构图](./images/acars-architecture.png)

### 2. 核心组件

#### 2.1 数据接入层（Data Ingestion Layer）

负责从各种来源接收ACARS报文数据，提供统一的数据入口。

- **多源适配器（Source Adapters）**：
  - 自建设备适配器
  - 机场Kafka适配器
  - Airframes抓取适配器
  - 通用HTTP/HTTPS接口适配器（用于未来扩展）
  - 通用Socket接口适配器（用于未来扩展）

- **数据接收服务（Receiver Service）**：
  - 提供统一的数据接收入口
  - 实现基本的数据验证和过滤
  - 将原始数据转发到消息队列

#### 2.2 消息中间件（Message Broker）

使用Kafka作为消息中间件，实现数据的缓冲和解耦。

- **原始数据主题（Raw Data Topics）**：
  - acars-raw-format-a：存储格式A的原始数据
  - acars-raw-format-b：存储格式B的原始数据
  - acars-raw-format-c：存储格式C的原始数据

- **标准化数据主题（Standardized Data Topic）**：
  - acars-standardized：存储经过格式转换后的标准化数据

#### 2.3 数据处理层（Data Processing Layer）

负责数据的解析、转换和标准化处理。

- **格式解析器（Format Parsers）**：
  - 格式A解析器
  - 格式B解析器
  - 格式C解析器
  - 可插拔式设计，支持新格式的快速集成

- **数据标准化服务（Standardization Service）**：
  - 将不同格式的数据转换为统一的内部标准格式
  - 实现数据清洗和验证
  - 处理数据异常和错误

- **数据富集服务（Enrichment Service）**：
  - 通过外部数据源补充额外信息
  - 关联航班、航线等业务数据
  - 提升数据价值

#### 2.4 数据存储层（Data Storage Layer）

提供多种存储选项，满足不同的数据访问需求。

- **实时数据库（Real-time Database）**：
  - 存储最新的ACARS报文数据
  - 支持高频读写和实时查询

- **历史数据库（Historical Database）**：
  - 存储历史ACARS报文数据
  - 支持大数据量的高效存储和查询

- **数据仓库（Data Warehouse）**：
  - 用于数据分析和报表生成
  - 支持复杂的聚合查询

#### 2.5 应用服务层（Application Service Layer）

提供各种业务应用服务，满足不同的业务需求。

- **API网关（API Gateway）**：
  - 提供统一的RESTful API接口
  - 实现API版本管理和访问控制

- **业务服务（Business Services）**：
  - 航班监控服务
  - 数据分析服务
  - 报警服务
  - 报表服务

#### 2.6 监控与管理（Monitoring & Management）

提供系统监控和管理功能，确保系统的稳定运行。

- **系统监控（System Monitoring）**：
  - 监控系统资源使用情况
  - 监控服务健康状态
  - 监控数据处理流程

- **告警系统（Alert System）**：
  - 设置多级告警规则
  - 支持多种告警通知方式

- **管理控制台（Management Console）**：
  - 提供系统配置管理界面
  - 提供数据源和解析器管理界面
  - 提供系统运行状态可视化界面

### 3. 数据流程

1. **数据接收**：
   - 各类数据源通过对应的适配器将数据发送到数据接收服务
   - 数据接收服务进行基本验证后，将原始数据按格式分类发送到对应的Kafka主题

2. **数据解析与标准化**：
   - 格式解析器从对应的Kafka主题消费原始数据
   - 解析器将原始数据解析为内部数据结构
   - 标准化服务将解析后的数据转换为统一标准格式
   - 标准化数据发送到标准化数据主题

3. **数据富集与存储**：
   - 数据富集服务从标准化数据主题消费数据
   - 通过外部数据源补充额外信息
   - 富集后的数据存储到实时数据库和历史数据库

4. **数据应用**：
   - 业务服务通过API网关访问数据
   - 数据分析服务从数据仓库获取数据进行分析
   - 监控与告警系统实时监控数据流和系统状态

### 4. 扩展性设计

#### 4.1 新数据源接入

1. 开发新的源适配器或使用通用适配器
2. 配置数据映射规则
3. 如果是新格式，开发新的格式解析器
4. 更新标准化服务的转换规则

#### 4.2 新业务应用接入

1. 在API网关注册新的API接口
2. 开发新的业务服务
3. 配置数据访问权限

### 5. 高可用设计

1. **服务冗余**：
   - 核心服务部署多个实例
   - 使用负载均衡分发请求

2. **数据备份**：
   - 实时数据库主从复制
   - 历史数据定期备份

3. **故障转移**：
   - 服务健康检查
   - 自动故障转移机制

### 6. 安全设计

1. **数据传输安全**：
   - 使用TLS/SSL加密传输
   - 实现数据完整性校验

2. **访问控制**：
   - 基于角色的访问控制
   - API访问认证和授权

3. **数据安全**：
   - 敏感数据加密存储
   - 数据访问审计日志

## 技术选型建议

1. **数据接入层**：
   - Spring Boot（Java）或FastAPI（Python）构建微服务
   - Apache Camel用于数据路由和转换

2. **消息中间件**：
   - Apache Kafka用于消息队列
   - Kafka Connect用于数据源连接

3. **数据处理层**：
   - Apache Flink用于流处理
   - Spring Cloud Stream用于消息处理

4. **数据存储层**：
   - MongoDB用于实时数据库（灵活的文档结构）
   - ClickHouse用于历史数据库（高效的列式存储）
   - Apache Druid用于数据仓库（实时分析能力）

5. **应用服务层**：
   - Spring Cloud Gateway用于API网关
   - Spring Boot用于业务服务开发

6. **监控与管理**：
   - Prometheus + Grafana用于系统监控
   - ELK Stack用于日志管理
   - Spring Boot Admin用于服务管理

## 实施路径

### 第一阶段：基础架构搭建

1. 搭建Kafka消息队列
2. 开发数据接收服务和基本适配器
3. 实现核心格式解析器
4. 搭建实时数据库

### 第二阶段：核心功能实现

1. 完善数据标准化服务
2. 开发数据富集服务
3. 搭建历史数据库
4. 开发基本API接口

### 第三阶段：功能完善与优化

1. 搭建数据仓库
2. 开发高级业务服务
3. 实现监控与告警系统
4. 开发管理控制台

### 第四阶段：系统集成与上线

1. 系统集成测试
2. 性能测试与优化
3. 数据迁移
4. 系统上线与监控

## 结论

通过采用这种模块化、可扩展的架构设计，可以有效解决当前ACARS报文数据处理面临的挑战：

1. **统一处理**：通过标准化服务将不同格式的数据转换为统一格式，简化后续处理流程
2. **灵活扩展**：采用插件式设计，支持新数据源和新格式的快速接入
3. **高可靠性**：通过服务冗余和故障转移机制，确保系统的高可用性
4. **可维护性**：清晰的模块划分和标准接口定义，提高系统的可维护性

该架构不仅能满足当前的需求，还能适应未来业务的发展和变化，为ACARS报文数据的处理提供一个长期可靠的解决方案。